package com.dahua.demo.basic;

import com.dahua.demo.util.BaseUserInfo;
import com.dahua.demo.util.HttpEnum;
import com.dahua.demo.util.HttpTestUtils;
import com.google.gson.Gson;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 增强版组织机构树监控器
 * 基于现有的OrgTree.getSub方法，增加实时监控URL获取功能
 * 
 * 使用方法：
 * 1. 确保baseinfo.properties中配置了正确的服务器信息
 * 2. 先运行Login类进行登录
 * 3. 运行KeepLogin类保持会话活跃
 * 4. 运行本类获取完整信息
 * 5. 最后运行LoginOut类登出
 */
public class EnhancedOrgTreeMonitor extends BaseUserInfo {
    
    private static final String ORG_ACTION = "/videoService/devicesManager/deviceTree";
    private static final String MONITOR_ACTION = "/videoService/realmonitor/uri";
    private static final String[] SUPPORTED_SCHEMES = {"RTSP", "FLV_HTTP", "HLS"};
    
    private static int orgLevel = -1; // 组织层级标志
    private static String indentMark = ""; // 打印结果的缩进标志
    private static List<ChannelInfo> allChannels = new ArrayList<>(); // 存储所有通道信息
    private static Gson gson = new Gson();
    
    // 通道信息类
    public static class ChannelInfo {
        public String channelId;
        public String channelName;
        public String orgName;
        public String orgId;
        public int orgLevel;
        
        public ChannelInfo(String channelId, String channelName, String orgName, String orgId, int level) {
            this.channelId = channelId;
            this.channelName = channelName;
            this.orgName = orgName;
            this.orgId = orgId;
            this.orgLevel = level;
        }
    }
    
    /**
     * 递归获取组织机构树和通道信息（基于OrgTree.getSub方法改进）
     */
    private static void getOrganizationTreeWithChannels(String parentOrgId) throws Exception {
        String content = "?id=" + parentOrgId + "&nodeType=1&typeCode=01&page=1&pageSize=100";
        String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, Integer.valueOf(port), ORG_ACTION, token, content);
        
        Map<String, Object> responseMap = gson.fromJson(response, Map.class);
        List<Map<String, Object>> organizations = (List<Map<String, Object>>) responseMap.get("results");
        
        if (organizations != null && !organizations.isEmpty()) {
            for (Map<String, Object> org : organizations) {
                orgLevel++;
                indentMark += "   ";
                
                String orgId = (String) org.get("id");
                String orgName = (String) org.get("orgName");
                
                System.out.println(indentMark + orgLevel + "级组织为: " + orgName + " = (组织编码)" + orgId);
                
                // 获取该组织下的通道信息（基于DevInfo.getOrgDevTree方法改进）
                getChannelsForOrganization(orgId, orgName, indentMark);
                
                // 递归获取子组织
                getOrganizationTreeWithChannels(orgId);
                
                indentMark = indentMark.substring(0, indentMark.length() - 3);
                orgLevel--;
            }
        }
    }
    
    /**
     * 获取指定组织下的通道信息（基于DevInfo.getOrgDevTree方法改进）
     */
    private static void getChannelsForOrganization(String orgId, String orgName, String mark) throws Exception {
        String content = "?id=" + orgId + "&nodeType=1&typeCode=01;0;ALL;ALL&page=1&pageSize=100";
        String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, Integer.valueOf(port), ORG_ACTION, token, content);
        
        Map<String, Object> responseMap = gson.fromJson(response, Map.class);
        List<Map<String, Object>> items = (List<Map<String, Object>>) responseMap.get("results");
        
        if (items != null && !items.isEmpty()) {
            DecimalFormat df = new DecimalFormat("######0");
            List<String> channelInfos = new ArrayList<>();
            
            for (Map<String, Object> item : items) {
                // 检查是否为通道类型 (nodeType = 3)
                if (3 == Integer.valueOf(df.format(item.get("nodeType")))) {
                    String channelId = (String) item.get("channelId");
                    String channelName = (String) item.get("channelName");
                    
                    if (channelId != null && channelName != null) {
                        allChannels.add(new ChannelInfo(channelId, channelName, orgName, orgId, orgLevel));
                        channelInfos.add("（通道名称）" + channelName + "=（通道编码）" + channelId);
                    }
                }
            }
            
            // 打印通道信息（保持与原有格式一致）
            if (!channelInfos.isEmpty()) {
                System.out.print("            " + mark);
                for (int i = 0; i < channelInfos.size(); i++) {
                    System.out.print(channelInfos.get(i));
                    if (i < channelInfos.size() - 1) {
                        System.out.print("; ");
                    }
                }
                System.out.println();
            }
        }
    }
    
    /**
     * 获取所有通道的监控URL
     */
    private static void getAllMonitorUrls() {
        if (allChannels.isEmpty()) {
            System.out.println("\n没有找到任何通道，无法获取监控URL");
            return;
        }
        
        System.out.println("\n=== 开始获取实时监控URL ===");
        System.out.println("总计找到 " + allChannels.size() + " 个通道");
        
        for (ChannelInfo channel : allChannels) {
            System.out.println("\n--- 通道: " + channel.channelName + " (组织: " + channel.orgName + ") ---");
            
            for (String scheme : SUPPORTED_SCHEMES) {
                try {
                    String url = getMonitorUrl(channel.channelId, scheme);
                    if (url != null && !url.trim().isEmpty()) {
                        System.out.println("  " + scheme + ": " + url);
                    }
                } catch (Exception e) {
                    System.err.println("  获取 " + scheme + " 监控URL失败: " + e.getMessage());
                }
            }
        }
        
        System.out.println("\n=== 实时监控URL获取完成 ===");
    }
    
    /**
     * 获取单个通道的监控URL
     */
    private static String getMonitorUrl(String channelId, String scheme) throws Exception {
        String content = "?channelId=" + channelId + "&scheme=" + scheme + "&subType=0";
        String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, Integer.valueOf(port), MONITOR_ACTION, token, content);
        
        if (response == null || response.trim().isEmpty()) {
            throw new Exception("收到空响应");
        }
        
        if (!response.startsWith("{")) {
            throw new Exception("收到非JSON响应: " + response);
        }
        
        Map<String, Object> responseMap = gson.fromJson(response, Map.class);
        Object urlObj = responseMap.get("url");
        
        if (urlObj != null) {
            return urlObj.toString();
        } else {
            throw new Exception("响应中未找到URL字段");
        }
    }
    
    /**
     * 主方法 - 获取组织机构树、通道信息和监控URL
     */
    public static void main(String[] args) throws Exception {
        System.out.println("=== 增强版组织机构树监控器 ===");
        System.out.println("功能：获取组织机构树、通道信息和实时监控URL");
        System.out.println("服务器地址: " + ip + ":" + port);
        System.out.println();
        
        // 检查token是否有效
        if (token == null || token.trim().isEmpty()) {
            System.err.println("错误: 未找到有效的token，请先运行Login类进行登录");
            System.err.println("使用步骤:");
            System.err.println("1. 运行 com.dahua.demo.login.Login 进行登录");
            System.err.println("2. 运行 com.dahua.demo.login.KeepLogin 保持会话活跃");
            System.err.println("3. 运行本程序获取信息");
            System.err.println("4. 运行 com.dahua.demo.login.LoginOut 登出");
            return;
        }
        
        try {
            System.out.println("=== 开始获取组织机构树和通道信息 ===");
            
            // 获取组织机构树和通道信息（从根组织开始）
            getOrganizationTreeWithChannels("");
            
            // 获取所有通道的监控URL
            getAllMonitorUrls();
            
            System.out.println("\n=== 所有信息获取完成 ===");
            System.out.println("提示：请确保KeepLogin程序保持运行状态以维持会话");
            
        } catch (Exception e) {
            System.err.println("执行过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
