package com.dahua.demo.basic;

import com.dahua.demo.util.BaseUserInfo;
import com.google.gson.Gson;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;
import java.io.*;
import java.net.URL;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

/**
 * 使用SSL证书的安全组织通道查询程序
 * 配合CertificateHttpsLogin使用
 */
public class CertificateOrgChannelQuery extends BaseUserInfo {
    
    private static final String ORG_TREE_ACTION = "/videoService/devicesManager/deviceTree";
    private static SSLContext sslContext = null;
    
    /**
     * 初始化SSL上下文，加载证书
     */
    private static synchronized void initSSLContext() throws Exception {
        if (sslContext != null) {
            return;
        }
        
        System.out.println("正在加载SSL证书...");
        
        // 创建证书工厂
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        
        // 加载根证书
        InputStream rootCertStream = CertificateOrgChannelQuery.class.getClassLoader()
                .getResourceAsStream("rootChain.crt");
        if (rootCertStream == null) {
            throw new Exception("找不到根证书文件: rootChain.crt");
        }
        Certificate rootCert = cf.generateCertificate(rootCertStream);
        rootCertStream.close();
        System.out.println("✅ 根证书加载成功");
        
        // 加载服务器证书
        InputStream serverCertStream = CertificateOrgChannelQuery.class.getClassLoader()
                .getResourceAsStream("key.cer");
        if (serverCertStream == null) {
            throw new Exception("找不到服务器证书文件: key.cer");
        }
        Certificate serverCert = cf.generateCertificate(serverCertStream);
        serverCertStream.close();
        System.out.println("✅ 服务器证书加载成功");
        
        // 创建KeyStore并添加证书
        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        keyStore.load(null, null);
        keyStore.setCertificateEntry("rootCA", rootCert);
        keyStore.setCertificateEntry("serverCert", serverCert);
        System.out.println("✅ 证书已添加到KeyStore");
        
        // 创建TrustManager
        TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        tmf.init(keyStore);
        
        // 创建SSL上下文
        sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, tmf.getTrustManagers(), null);
        System.out.println("✅ SSL上下文初始化完成");
    }
    
    /**
     * 发送安全HTTPS GET请求
     */
    private static String sendSecureHttpsGet(String token, String queryParams) {
        try {
            initSSLContext();
            
            // 构建HTTPS URL
            String httpsUrl = "https://" + ip + ":" + port + ORG_TREE_ACTION + queryParams;
            URL url = new URL(httpsUrl);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            // 设置SSL上下文
            connection.setSSLSocketFactory(sslContext.getSocketFactory());
            
            // 设置主机名验证器
            connection.setHostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    return true; // 允许IP地址连接
                }
            });
            
            // 设置请求头
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-Subject-Token", token);
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(30000);    // 30秒读取超时
            
            // 检查响应码
            int responseCode = connection.getResponseCode();
            
            // 读取响应
            InputStream inputStream;
            if (responseCode >= 200 && responseCode < 300) {
                inputStream = connection.getInputStream();
            } else {
                inputStream = connection.getErrorStream();
                if (inputStream == null) {
                    inputStream = connection.getInputStream();
                }
            }
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            
            System.out.println("HTTP响应码: " + responseCode);
            if (responseCode != 200) {
                if (responseCode == 401) {
                    System.err.println("❌ Token已过期，请重新运行CertificateHttpsLogin获取新Token");
                } else {
                    System.err.println("❌ HTTP请求失败，响应码: " + responseCode);
                }
            }
            
            return response.toString();
            
        } catch (Exception e) {
            System.err.println("安全HTTPS查询请求失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 递归查询组织树
     */
    private static void queryOrganizationTree(String parentId, int level, String token) {
        try {
            // 构建查询参数
            String queryParams = "?id=" + parentId + "&nodeType=1&typeCode=01&page=1&pageSize=100";
            
            // 发送安全HTTPS请求
            String response = sendSecureHttpsGet(token, queryParams);
            
            if (response == null || response.trim().isEmpty()) {
                System.err.println("  " + "  ".repeat(level - 1) + "└─ ❌ 收到空响应");
                return;
            }
            
            if (!response.startsWith("{")) {
                System.err.println("  " + "  ".repeat(level - 1) + "└─ ❌ 收到非JSON响应: " + response);
                return;
            }

            System.out.println("  " + "  ".repeat(level - 1) + "API响应: " + response.substring(0, Math.min(200, response.length())) + "...");
            
            // 解析响应
            Map<String, Object> responseMap = new Gson().fromJson(response, Map.class);

            // 检查是否有message字段（错误响应）
            String message = (String) responseMap.get("message");
            if (message != null && !"success".equals(message)) {
                System.err.println("  " + "  ".repeat(level - 1) + "└─ ❌ API调用失败: " + message);
                return;
            }

            // 直接从响应中获取results数组（成功响应）
            List<Map<String, Object>> items = (List<Map<String, Object>>) responseMap.get("results");
            if (items == null || items.isEmpty()) {
                if (level == 1) {
                    System.out.println("  └─ (未找到组织节点)");
                }
                return;
            }
            
            // 显示当前层级的节点
            for (int i = 0; i < items.size(); i++) {
                Map<String, Object> item = items.get(i);
                String id = (String) item.get("id");
                String name = (String) item.get("name");
                String nodeType = String.valueOf(item.get("nodeType"));
                String typeCode = (String) item.get("typeCode");
                
                // 构建树形显示
                String prefix = "  ".repeat(level - 1);
                String connector = (i == items.size() - 1) ? "└─" : "├─";
                
                // 根据节点类型显示不同图标
                String icon = getNodeIcon(nodeType, typeCode);
                
                System.out.println(prefix + connector + " " + icon + " " + name + 
                    " [ID:" + id + ", 类型:" + nodeType + ", 代码:" + typeCode + "]");
                
                // 如果是组织节点，递归查询子节点
                if ("1".equals(nodeType)) {
                    queryOrganizationTree(id, level + 1, token);
                }
            }
            
        } catch (Exception e) {
            System.err.println("  " + "  ".repeat(level - 1) + "└─ ❌ 查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据节点类型获取显示图标
     */
    private static String getNodeIcon(String nodeType, String typeCode) {
        if ("1".equals(nodeType)) {
            return "📁"; // 组织节点
        } else if ("2".equals(nodeType)) {
            if ("01".equals(typeCode)) {
                return "📹"; // 摄像头
            } else if ("02".equals(typeCode)) {
                return "🎤"; // 音频设备
            } else {
                return "📱"; // 其他设备
            }
        }
        return "❓"; // 未知类型
    }
    
    /**
     * 主查询方法
     */
    public static void queryAllOrganizations(String token) {
        try {
            System.out.println("=".repeat(80));
            System.out.println("        大华开放平台 - 安全SSL证书组织通道查询");
            System.out.println("=".repeat(80));
            System.out.println("服务器地址: " + ip + ":" + port + " (安全HTTPS)");
            System.out.println("Token: " + token.substring(0, Math.min(30, token.length())) + "...");
            System.out.println("使用证书: rootChain.crt + key.cer");
            System.out.println();
            
            System.out.println("正在查询组织树结构...");
            queryOrganizationTree("", 1, token);
            
            System.out.println();
            System.out.println("=".repeat(80));
            System.out.println("🎉 安全HTTPS查询完成！");
            System.out.println("=".repeat(80));
            
        } catch (Exception e) {
            System.err.println("\n❌ 查询失败: " + e.getMessage());
            System.err.println("\n错误处理建议:");
            System.err.println("1. 检查Token是否有效（有效期2分钟）");
            System.err.println("2. 如Token过期，请重新运行CertificateHttpsLogin");
            System.err.println("3. 检查SSL证书文件是否存在");
            System.err.println("4. 确认网络连接正常");
            e.printStackTrace();
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        System.out.println("启动安全SSL证书组织通道查询程序...");
        System.out.println();
        
        String token = null;
        
        // 检查命令行参数
        if (args.length > 0) {
            token = args[0];
            System.out.println("使用命令行提供的Token: " + token.substring(0, Math.min(30, token.length())) + "...");
        } else {
            // 从用户输入获取Token
            Scanner scanner = new Scanner(System.in);
            System.out.println("请输入Token（从CertificateHttpsLogin获取）:");
            System.out.print("Token: ");
            token = scanner.nextLine().trim();
            scanner.close();
        }
        
        if (token == null || token.trim().isEmpty()) {
            System.err.println("❌ Token不能为空");
            System.err.println("请先运行CertificateHttpsLogin获取Token");
            return;
        }
        
        System.out.println();
        queryAllOrganizations(token);
    }
}
