package com.dahua.demo.basic;

import com.dahua.demo.util.BaseUserInfo;
import com.dahua.demo.util.HttpEnum;
import com.dahua.demo.util.HttpTestUtils;

/**
 * Token检查工具
 * 用于检查当前Token的有效性和登录状态
 */
public class TokenChecker extends BaseUserInfo {
    
    /**
     * 检查Token有效性
     */
    public static void checkTokenStatus() {
        System.out.println("=== Token状态检查 ===");
        System.out.println("服务器地址: " + ip + ":" + port);
        System.out.println("用户名: " + userName);
        
        if (token == null || token.trim().isEmpty()) {
            System.err.println("❌ 未找到Token，请先运行Login类进行登录");
            System.out.println("\n登录步骤:");
            System.out.println("1. 运行: java com.dahua.demo.login.Login");
            System.out.println("2. 运行: java com.dahua.demo.login.KeepLogin (保持运行)");
            return;
        }
        
        System.out.println("Token: " + token.substring(0, Math.min(20, token.length())) + "...");
        
        // 测试Token有效性 - 尝试获取根组织
        try {
            System.out.println("\n正在测试Token有效性...");
            String content = "?id=&nodeType=1&typeCode=01&page=1&pageSize=10";
            String response = HttpTestUtils.httpRequest(
                HttpEnum.GET, 
                ip, 
                Integer.valueOf(port), 
                "/videoService/devicesManager/deviceTree", 
                token, 
                content
            );
            
            System.out.println("API响应: " + response);
            
            if (response == null || response.trim().isEmpty()) {
                System.err.println("❌ 收到空响应，可能网络连接问题");
            } else if (response.contains("未登录") || response.contains("token") || response.contains("登录")) {
                System.err.println("❌ Token已过期或无效，请重新登录");
                System.out.println("\n请按以下步骤重新登录:");
                System.out.println("1. 运行: java com.dahua.demo.login.Login");
                System.out.println("2. 运行: java com.dahua.demo.login.KeepLogin (保持运行)");
            } else if (response.contains("权限") || response.contains("403")) {
                System.err.println("❌ 用户权限不足");
            } else if (response.startsWith("{")) {
                System.out.println("✅ Token有效，可以正常访问API");
            } else {
                System.err.println("❌ 收到未知响应格式: " + response);
            }
            
        } catch (Exception e) {
            System.err.println("❌ 测试Token时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        checkTokenStatus();
    }
}
