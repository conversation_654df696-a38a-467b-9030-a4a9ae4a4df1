# 组织通道监控URL获取工具使用说明

## 功能概述

本工具实现了您要求的完整功能：
1. **获取所有当前环境的组织机构树** - 递归获取完整的组织层级结构
2. **获取每个具体机构所包含的通道** - 为每个组织获取其下属的所有通道信息
3. **获取实时监控的URL并打印出来** - 为每个通道获取RTSP、FLV_HTTP、HLS三种协议的监控地址

## 文件说明

### 主要文件
- `EnhancedOrgTreeMonitor.java` - **推荐使用**，基于现有OrgTree.getSub方法的增强版本
- `ComprehensiveMonitorManager.java` - 完整的管理器版本，包含自动登录登出功能
- `RealTimeMonitorService.java` - 实时监控服务类

### 推荐使用方案：EnhancedOrgTreeMonitor

这个类基于您提到的现有 `OrgTree.getSub` 方法进行了增强，完全符合厂商提供的使用方式。

## 使用步骤

### 方案一：使用 EnhancedOrgTreeMonitor（推荐）

按照厂商提供的标准流程：

#### 1. 配置服务器信息
确保 `src/main/resources/baseinfo.properties` 文件中配置正确：
```properties
ip=**************
port=7282
userName=haishi
password=haishi@123
```

#### 2. 登录
```bash
# 运行登录程序
java com.dahua.demo.login.Login
```

#### 3. 启动保活（重要！）
```bash
# 运行保活程序，必须保持运行状态
java com.dahua.demo.login.KeepLogin
```
**注意：KeepLogin程序必须在执行状态，不能关闭！**

#### 4. 运行组织通道监控URL获取工具
```bash
# 运行主程序
java com.dahua.demo.basic.EnhancedOrgTreeMonitor
```

#### 5. 登出
```bash
# 完成后运行登出程序
java com.dahua.demo.login.LoginOut
```

### 方案二：使用 ComprehensiveMonitorManager（自动化）

如果您希望自动处理登录登出流程：

```bash
java com.dahua.demo.basic.ComprehensiveMonitorManager
```

这个版本会自动：
- 登录获取token
- 启动保活线程
- 获取组织和通道信息
- 获取监控URL
- 自动登出和清理资源

## 输出示例

### 组织机构树输出
```
=== 开始获取组织机构树和通道信息 ===
0级组织为: 根组织 = (组织编码)ROOT_ORG_001
            （通道名称）大门摄像头=（通道编码）CH_001; （通道名称）停车场摄像头=（通道编码）CH_002; 
   1级组织为: 办公楼 = (组织编码)OFFICE_001
            （通道名称）会议室摄像头=（通道编码）CH_003; 
      2级组织为: 一楼 = (组织编码)FLOOR_001
            （通道名称）大厅摄像头=（通道编码）CH_004; 
```

### 监控URL输出
```
=== 开始获取实时监控URL ===
总计找到 4 个通道

--- 通道: 大门摄像头 (组织: 根组织) ---
  RTSP: rtsp://**************:554/cam/realmonitor?channel=1&subtype=0
  FLV_HTTP: http://**************:80/cam/realmonitor.flv?channel=1&subtype=0
  HLS: http://**************:80/cam/realmonitor.m3u8?channel=1&subtype=0

--- 通道: 停车场摄像头 (组织: 根组织) ---
  RTSP: rtsp://**************:554/cam/realmonitor?channel=2&subtype=0
  FLV_HTTP: http://**************:80/cam/realmonitor.flv?channel=2&subtype=0
  HLS: http://**************:80/cam/realmonitor.m3u8?channel=2&subtype=0
```

## 技术特性

### EnhancedOrgTreeMonitor 特性
- 基于现有的 `OrgTree.getSub` 方法，保持与原有代码风格一致
- 基于现有的 `DevInfo.getOrgDevTree` 方法获取通道信息
- 支持三种监控协议：RTSP、FLV_HTTP、HLS
- 递归获取完整的组织机构树
- 结构化输出，层级清晰
- 完整的错误处理

### ComprehensiveMonitorManager 特性
- 自动登录和会话管理
- 自动保活线程管理
- 自动登出和资源清理
- 完整的异常处理机制

## 注意事项

1. **保活程序必须运行**：按照厂商要求，KeepLogin程序必须在执行状态，登录保活时间为110秒
2. **Token有效性**：如果出现"未登录"提示，请重新运行Login程序
3. **网络连接**：确保能够正常访问配置的服务器地址和端口
4. **权限检查**：确保用户账号有足够权限访问组织和通道信息

## 故障排除

### 常见错误及解决方案

1. **"未找到有效的token"**
   - 解决：运行 `Login` 类进行登录

2. **"收到非JSON响应"**
   - 可能原因：Token过期或权限不足
   - 解决：重新登录或检查用户权限

3. **"获取监控URL失败"**
   - 可能原因：通道不在线或不支持该协议
   - 解决：检查通道状态或尝试其他协议

4. **程序运行缓慢**
   - 原因：需要递归获取大量组织和通道信息
   - 这是正常现象，请耐心等待

## 扩展功能

如需自定义功能，可以修改以下参数：

### 支持的监控协议
```java
private static final String[] SUPPORTED_SCHEMES = {"RTSP", "FLV_HTTP", "HLS"};
```

### 码流类型
```java
// 在getMonitorUrl方法中修改subType参数
// 0:主码流、1:辅流1、2:辅流2
String content = "?channelId=" + channelId + "&scheme=" + scheme + "&subType=0";
```

## 联系支持

如果遇到问题，请检查：
1. 服务器配置是否正确
2. 网络连接是否正常
3. 用户权限是否足够
4. KeepLogin程序是否正在运行
