package com.dahua.demo.basic;

import com.dahua.demo.util.BaseUserInfo;
import com.dahua.demo.util.HttpEnum;
import com.dahua.demo.util.HttpTestUtils;
import com.google.gson.Gson;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 综合监控管理器
 * 基于现有的OrgTree和DevInfo类，实现完整的组织机构树、通道信息和实时监控URL获取功能
 *
 * 功能包括：
 * 1. 自动登录和会话管理
 * 2. 获取完整的组织机构树（基于OrgTree.getSub方法）
 * 3. 获取每个组织的通道信息（基于DevInfo.getOrgDevTree方法）
 * 4. 获取每个通道的实时监控URL
 * 5. 结构化输出所有信息
 * 6. 自动登出和资源清理
 */
public class ComprehensiveMonitorManager extends BaseUserInfo {

    private static final String ORG_ACTION = "/videoService/devicesManager/deviceTree";
    private static final String MONITOR_ACTION = "/videoService/realmonitor/uri";
    private static final String[] SUPPORTED_SCHEMES = {"RTSP", "FLV_HTTP", "HLS"};

    private Thread keepAliveThread;
    private AtomicBoolean keepAliveRunning;
    private Gson gson;
    private List<ChannelInfo> allChannels; // 存储所有通道信息

    // 通道信息类
    public static class ChannelInfo {
        public String channelId;
        public String channelName;
        public String orgName;
        public String orgId;

        public ChannelInfo(String channelId, String channelName, String orgName, String orgId) {
            this.channelId = channelId;
            this.channelName = channelName;
            this.orgName = orgName;
            this.orgId = orgId;
        }
    }

    public ComprehensiveMonitorManager() {
        this.keepAliveRunning = new AtomicBoolean(false);
        this.gson = new Gson();
        this.allChannels = new ArrayList<>();
    }
    
    /**
     * 执行完整的监控信息获取流程
     */
    public void execute() {
        System.out.println("=== 开始执行综合监控信息获取 ===");
        System.out.println("服务器地址: " + ip + ":" + port);
        System.out.println("用户名: " + userName);

        try {
            // 1. 登录获取token
            if (!login()) {
                System.err.println("登录失败，程序终止");
                return;
            }

            // 2. 启动保活线程
            startKeepAlive();

            // 3. 获取组织机构树和通道信息（基于现有的getSub方法）
            System.out.println("\n=== 开始获取组织机构树和通道信息 ===");
            getAllOrganizationsAndChannels("");

            if (allChannels.isEmpty()) {
                System.out.println("未找到任何通道信息");
                return;
            }

            // 4. 获取所有通道的监控URL
            System.out.println("\n=== 开始获取实时监控URL ===");
            getAllMonitorUrls();

            System.out.println("\n=== 综合监控信息获取完成 ===");

        } catch (Exception e) {
            System.err.println("执行过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 6. 清理资源
            cleanup();
        }
    }
    
    /**
     * 登录获取token
     */
    private boolean login() {
        try {
            System.out.println("正在登录...");
            String newToken = HttpTestUtils.getToken(ip, Integer.valueOf(port), userName, password);
            
            if (newToken != null && !newToken.trim().isEmpty()) {
                // 更新BaseUserInfo中的token
                token = newToken;
                System.out.println("登录成功！");
                System.out.println("Token: " + token.substring(0, Math.min(20, token.length())) + "...");
                return true;
            } else {
                System.err.println("登录失败：未获取到有效token");
                return false;
            }
        } catch (Exception e) {
            System.err.println("登录过程中发生错误: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 启动保活线程
     */
    private void startKeepAlive() {
        keepAliveRunning.set(true);
        keepAliveThread = new Thread(() -> {
            System.out.println("启动会话保活线程...");
            while (keepAliveRunning.get()) {
                try {
                    // 每110秒发送一次保活请求
                    Thread.sleep(110000);
                    if (keepAliveRunning.get()) {
                        String content = "{\"token\":\"" + token + "\"}";
                        String response = HttpTestUtils.httpRequest(
                            com.dahua.demo.util.HttpEnum.PUT, 
                            ip, 
                            Integer.valueOf(port), 
                            "/videoService/accounts/token/keepalive", 
                            token, 
                            content
                        );
                        System.out.println("会话保活成功: " + response);
                    }
                } catch (InterruptedException e) {
                    System.out.println("保活线程被中断");
                    break;
                } catch (Exception e) {
                    System.err.println("会话保活失败: " + e.getMessage());
                }
            }
            System.out.println("会话保活线程已停止");
        });
        keepAliveThread.setDaemon(true);
        keepAliveThread.start();
    }
    
    /**
     * 停止保活线程
     */
    private void stopKeepAlive() {
        if (keepAliveRunning.get()) {
            System.out.println("正在停止会话保活线程...");
            keepAliveRunning.set(false);
            if (keepAliveThread != null) {
                keepAliveThread.interrupt();
                try {
                    keepAliveThread.join(5000); // 等待最多5秒
                } catch (InterruptedException e) {
                    System.err.println("等待保活线程停止时被中断");
                }
            }
        }
    }
    
    /**
     * 递归获取所有组织和通道信息（基于OrgTree.getSub和DevInfo.getOrgDevTree方法）
     */
    private void getAllOrganizationsAndChannels(String parentOrgId) throws Exception {
        // 获取组织信息
        String content = "?id=" + parentOrgId + "&nodeType=1&typeCode=01&page=1&pageSize=100";
        String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, Integer.valueOf(port), ORG_ACTION, token, content);

        Map<String, Object> responseMap = gson.fromJson(response, Map.class);
        List<Map<String, Object>> organizations = (List<Map<String, Object>>) responseMap.get("results");

        if (organizations != null && !organizations.isEmpty()) {
            for (Map<String, Object> org : organizations) {
                String orgId = (String) org.get("id");
                String orgName = (String) org.get("orgName");

                System.out.println("组织: " + orgName + " (ID: " + orgId + ")");

                // 获取该组织下的通道信息
                getChannelsForOrganization(orgId, orgName);

                // 递归获取子组织
                getAllOrganizationsAndChannels(orgId);
            }
        }
    }

    /**
     * 获取指定组织下的通道信息（基于DevInfo.getOrgDevTree方法）
     */
    private void getChannelsForOrganization(String orgId, String orgName) throws Exception {
        String content = "?id=" + orgId + "&nodeType=1&typeCode=01;0;ALL;ALL&page=1&pageSize=100";
        String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, Integer.valueOf(port), ORG_ACTION, token, content);

        Map<String, Object> responseMap = gson.fromJson(response, Map.class);
        List<Map<String, Object>> items = (List<Map<String, Object>>) responseMap.get("results");

        if (items != null && !items.isEmpty()) {
            DecimalFormat df = new DecimalFormat("######0");
            int channelCount = 0;

            for (Map<String, Object> item : items) {
                // 检查是否为通道类型 (nodeType = 3)
                if (3 == Integer.valueOf(df.format(item.get("nodeType")))) {
                    String channelId = (String) item.get("channelId");
                    String channelName = (String) item.get("channelName");

                    if (channelId != null && channelName != null) {
                        allChannels.add(new ChannelInfo(channelId, channelName, orgName, orgId));
                        channelCount++;
                        System.out.println("  通道: " + channelName + " (ID: " + channelId + ")");
                    }
                }
            }

            if (channelCount == 0) {
                System.out.println("  该组织下没有通道");
            }
        }
    }

    /**
     * 获取所有通道的监控URL
     */
    private void getAllMonitorUrls() {
        System.out.println("总计找到 " + allChannels.size() + " 个通道，开始获取监控URL...");

        for (ChannelInfo channel : allChannels) {
            System.out.println("\n--- 通道: " + channel.channelName + " (组织: " + channel.orgName + ") ---");

            for (String scheme : SUPPORTED_SCHEMES) {
                try {
                    String url = getMonitorUrl(channel.channelId, scheme);
                    if (url != null && !url.trim().isEmpty()) {
                        System.out.println("  " + scheme + ": " + url);
                    }
                } catch (Exception e) {
                    System.err.println("  获取 " + scheme + " 监控URL失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 获取单个通道的监控URL
     */
    private String getMonitorUrl(String channelId, String scheme) throws Exception {
        String content = "?channelId=" + channelId + "&scheme=" + scheme + "&subType=0";
        String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, Integer.valueOf(port), MONITOR_ACTION, token, content);

        if (response == null || response.trim().isEmpty()) {
            throw new Exception("收到空响应");
        }

        if (!response.startsWith("{")) {
            throw new Exception("收到非JSON响应: " + response);
        }

        Map<String, Object> responseMap = gson.fromJson(response, Map.class);
        Object urlObj = responseMap.get("url");

        if (urlObj != null) {
            return urlObj.toString();
        } else {
            throw new Exception("响应中未找到URL字段");
        }
    }
    
    /**
     * 登出
     */
    private void logout() {
        try {
            System.out.println("正在登出...");
            String content = "{\"token\":\"" + token + "\"}";
            String response = HttpTestUtils.httpRequest(
                com.dahua.demo.util.HttpEnum.POST, 
                ip, 
                Integer.valueOf(port), 
                "/videoService/accounts/unauthorize", 
                token, 
                content
            );
            System.out.println("登出成功: " + response);
        } catch (Exception e) {
            System.err.println("登出过程中发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        System.out.println("\n=== 开始清理资源 ===");
        
        // 停止保活线程
        stopKeepAlive();
        
        // 登出
        logout();
        
        System.out.println("=== 资源清理完成 ===");
    }
    
    /**
     * 主入口方法
     */
    public static void main(String[] args) {
        System.out.println("综合监控管理器启动");
        System.out.println("功能：获取组织机构树、通道信息和实时监控URL");
        System.out.println();
        
        ComprehensiveMonitorManager manager = new ComprehensiveMonitorManager();
        manager.execute();
    }
}
