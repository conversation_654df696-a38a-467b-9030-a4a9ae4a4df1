package com.dahua.demo.basic;

import com.dahua.demo.util.BaseUserInfo;
import com.dahua.demo.util.HttpEnum;
import com.dahua.demo.util.HttpTestUtils;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 实时监控服务类
 * 提供获取通道实时监控URL的功能
 */
public class RealTimeMonitorService extends BaseUserInfo {
    
    private static final String ACTION = "/videoService/realmonitor/uri";
    private static final String[] SUPPORTED_SCHEMES = {"RTSP", "FLV_HTTP", "HLS"};
    private static final int DEFAULT_SUB_TYPE = 0; // 主码流
    
    private Gson gson;
    
    public RealTimeMonitorService() {
        this.gson = new Gson();
    }
    
    /**
     * 监控URL信息类
     */
    public static class MonitorUrl {
        private String channelId;
        private String channelName;
        private String scheme;
        private String url;
        private int subType;
        
        public MonitorUrl(String channelId, String channelName, String scheme, String url, int subType) {
            this.channelId = channelId;
            this.channelName = channelName;
            this.scheme = scheme;
            this.url = url;
            this.subType = subType;
        }
        
        // Getter methods
        public String getChannelId() { return channelId; }
        public String getChannelName() { return channelName; }
        public String getScheme() { return scheme; }
        public String getUrl() { return url; }
        public int getSubType() { return subType; }
        
        @Override
        public String toString() {
            return String.format("通道[%s] %s协议: %s", channelName, scheme, url);
        }
    }
    
    /**
     * 为单个通道获取所有支持协议的监控URL
     * @param channel 通道信息
     * @return 监控URL列表
     */
    public List<MonitorUrl> getMonitorUrlsForChannel(Channel channel) {
        List<MonitorUrl> urls = new ArrayList<>();
        
        if (channel == null || channel.getChannelId() == null) {
            System.err.println("通道信息无效，跳过获取监控URL");
            return urls;
        }
        
        for (String scheme : SUPPORTED_SCHEMES) {
            try {
                String url = getMonitorUrl(channel.getChannelId(), scheme, DEFAULT_SUB_TYPE);
                if (url != null && !url.trim().isEmpty()) {
                    urls.add(new MonitorUrl(
                        channel.getChannelId(), 
                        channel.getChannelName(), 
                        scheme, 
                        url, 
                        DEFAULT_SUB_TYPE
                    ));
                }
            } catch (Exception e) {
                System.err.println("获取通道 " + channel.getChannelName() + 
                                 " 的 " + scheme + " 监控URL失败: " + e.getMessage());
            }
        }
        
        return urls;
    }
    
    /**
     * 为通道列表批量获取监控URL
     * @param channels 通道列表
     * @return 监控URL列表
     */
    public List<MonitorUrl> getMonitorUrlsForChannels(List<Channel> channels) {
        List<MonitorUrl> allUrls = new ArrayList<>();
        
        if (channels == null || channels.isEmpty()) {
            return allUrls;
        }
        
        System.out.println("开始为 " + channels.size() + " 个通道获取监控URL...");
        
        for (Channel channel : channels) {
            List<MonitorUrl> channelUrls = getMonitorUrlsForChannel(channel);
            allUrls.addAll(channelUrls);
        }
        
        System.out.println("成功获取 " + allUrls.size() + " 个监控URL");
        return allUrls;
    }
    
    /**
     * 获取单个通道指定协议的监控URL
     * @param channelId 通道ID
     * @param scheme 协议类型 (RTSP, FLV_HTTP, HLS)
     * @param subType 码流类型 (0:主码流、1:辅流1、2:辅流2)
     * @return 监控URL
     * @throws Exception 当API调用失败时抛出异常
     */
    private String getMonitorUrl(String channelId, String scheme, int subType) throws Exception {
        String content = "?channelId=" + channelId + 
                        "&scheme=" + scheme + 
                        "&subType=" + subType;
        
        String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, Integer.valueOf(port), ACTION, token, content);
        
        if (response == null || response.trim().isEmpty()) {
            throw new Exception("收到空响应");
        }
        
        // 检查响应是否为有效JSON
        if (!response.startsWith("{")) {
            throw new Exception("收到非JSON响应: " + response);
        }
        
        try {
            Map<String, Object> responseMap = gson.fromJson(response, Map.class);
            Object urlObj = responseMap.get("url");
            
            if (urlObj != null) {
                return urlObj.toString();
            } else {
                throw new Exception("响应中未找到URL字段");
            }
        } catch (Exception e) {
            throw new Exception("解析响应JSON失败: " + e.getMessage());
        }
    }
    
    /**
     * 打印监控URL信息
     * @param urls 监控URL列表
     */
    public void printMonitorUrls(List<MonitorUrl> urls) {
        if (urls == null || urls.isEmpty()) {
            System.out.println("没有找到任何监控URL");
            return;
        }
        
        System.out.println("\n=== 实时监控URL信息 ===");
        System.out.println("总计找到 " + urls.size() + " 个监控URL:");
        
        String currentChannelId = "";
        for (MonitorUrl url : urls) {
            if (!url.getChannelId().equals(currentChannelId)) {
                currentChannelId = url.getChannelId();
                System.out.println("\n通道: " + url.getChannelName() + " (ID: " + url.getChannelId() + ")");
            }
            System.out.println("  " + url.getScheme() + ": " + url.getUrl());
        }
        System.out.println("\n=== 监控URL信息结束 ===\n");
    }
}
